import { PerkService } from "../perk/perk.service"
import { PerkSubscriptionService } from "./perk-subscription.service"
import { AI_USAGE_LIMITS } from "../user-ai-usage/user-ai-usage.types"
import { SubscriptionAggregationService } from "./subscription-aggregation.service"

/**
 * Enhanced subscription service that considers both subscription status and active perks
 */
export class PerkAwareSubscriptionService {
  /**
   * Get enhanced user limits considering both subscription and perks
   * @deprecated Use SubscriptionAggregationService.getEnhancedSubscriptionLimits instead
   */
  static async getEnhancedUserLimits(userId: string) {
    // Simply delegate to the new service
    return await SubscriptionAggregationService.getEnhancedSubscriptionLimits(userId)
  }

  /**
   * Check if user can create more squads (perk-aware)
   */
  static async canCreateMoreSquads(userId: string, currentSquadCount?: number): Promise<boolean> {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      // If we have infinite squads, return true
      if (limits.maxSquads === Infinity) return true

      // If we already have the count, use it
      if (typeof currentSquadCount === "number") {
        return currentSquadCount < limits.maxSquads
      }

      // Otherwise, fetch the current count
      const { SquadService } = await import("../squad/squad.service")
      const userSquads = await SquadService.getUserSquads(userId)
      return userSquads.length < limits.maxSquads
    } catch (error) {
      console.error("Error checking if user can create more squads:", error)
      return false
    }
  }

  /**
   * Check if user can create more trips in a squad (perk-aware)
   */
  static async canCreateMoreTripsInSquad(
    userId: string,
    squadId: string,
    currentTripCount?: number
  ): Promise<boolean> {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      // If we have infinite trips, return true
      if (limits.maxTripsPerSquad === Infinity) return true

      // If we already have the count, use it
      if (typeof currentTripCount === "number") {
        return currentTripCount < limits.maxTripsPerSquad
      }

      // Otherwise, fetch the current count
      const { TripService } = await import("../trip/trip.service")
      const squadTrips = await TripService.getSquadTrips(squadId)

      // Only count non-completed trips (planning, upcoming, active)
      const activeTrips = squadTrips.filter(
        (trip) =>
          trip.status === "planning" || trip.status === "upcoming" || trip.status === "active"
      )

      return activeTrips.length < limits.maxTripsPerSquad
    } catch (error) {
      console.error("Error checking if user can create more trips in squad:", error)
      return false
    }
  }

  /**
   * Get enhanced AI usage limits (perk-aware)
   */
  static async getEnhancedAILimits(userId: string) {
    try {
      const limits = await this.getEnhancedUserLimits(userId)

      return {
        dailyLimit: limits.maxDailyAIRequests,
        weeklyLimit: limits.maxWeeklyAIRequests,
        perkEnhancements: limits.perkEnhancements,
      }
    } catch (error) {
      console.error("Error getting enhanced AI limits:", error)
      return {
        dailyLimit: AI_USAGE_LIMITS.FREE.DAILY,
        weeklyLimit: AI_USAGE_LIMITS.FREE.WEEKLY,
        perkEnhancements: {
          additionalDailyAI: 0,
          additionalWeeklyAI: 0,
          activePerkIds: [],
        },
      }
    }
  }

  /**
   * Apply subscription perks on login
   */
  static async applySubscriptionPerksOnLogin(userId: string): Promise<void> {
    try {
      // Get user's unlocked subscription perks that haven't been applied
      const userPerks = await PerkService.getUserPerks(userId)
      const unlockedSubscriptionPerks = userPerks.filter(
        (perk) =>
          perk.status === "unlocked" &&
          perk.perkDetails.perkType === "subscription" &&
          perk.perkDetails.tags.includes("subscription")
      )

      // Apply each unlocked subscription perk
      for (const perk of unlockedSubscriptionPerks) {
        try {
          const result = await PerkSubscriptionService.applySubscriptionPerk(userId, perk.perkId)
          if (result.success) {
            console.log(`Applied subscription perk ${perk.perkId} for user ${userId}`)
            console.log(`Subscription perk expires at: ${result.expiresAt}`)
          }
        } catch (error) {
          console.error(`Error applying subscription perk ${perk.perkId}:`, error)
        }
      }
    } catch (error) {
      console.error("Error applying subscription perks on login:", error)
    }
  }
}
